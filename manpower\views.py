from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from rest_framework.response import Response
from django.db.models import Q

from .models import ManpowerQualifications
from .serializers import ManpowerQualificationsSerializer


@extend_schema(
    tags=["Manpower"]
)
class ManpowerQualificationsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ManpowerQualifications model providing CRUD operations.
    """
    queryset = ManpowerQualifications.objects.all()
    serializer_class = ManpowerQualificationsSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartPars<PERSON>, FormParser, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    search_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    
    def list(self, request, *args, **kwargs):
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        organization = request.query_params.get('organization')
        qualification = request.query_params.get('qualification')
        fieldOfStudy = request.query_params.get('fieldOfStudy')
        experienceYears = request.query_params.get('experienceYears')
        skill = request.query_params.get('skill')
        isCertification = request.query_params.get('isCertification')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        
        queryset = self.get_queryset()
        if code:
            queryset = queryset.filter(code__icontains=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if organization:
            queryset = queryset.filter(organization__icontains=organization)
        if qualification:
            queryset = queryset.filter(qualification=qualification)
        if fieldOfStudy:
            queryset = queryset.filter(fieldOfStudy__icontains=fieldOfStudy)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if skill:
            queryset = queryset.filter(skill__icontains=skill)
        if isCertification:
            queryset = queryset.filter(isCertification=isCertification)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
