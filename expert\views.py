from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from django.db.models import Q
from django.db import transaction
from datetime import datetime
from testings.models import Runningnumber
from .models import Expert
from .serializers import ExpertSerializer


@extend_schema(
    tags=["Expert"]
)
class ExpertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Expert model providing CRUD operations.
    Supports file uploads for certifications field.
    """
    queryset = Expert.objects.all()
    serializer_class = ExpertSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [<PERSON>Part<PERSON>ars<PERSON>, FormParser, JSONParser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['code','name','affiliation','specialization','experienceYears','status']
    search_fields = ['code','name','affiliation','specialization','experienceYears','status']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        affiliation = request.query_params.get('affiliation')
        specialization = request.query_params.get('specialization')
        experienceYears = request.query_params.get('experienceYears')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        
        if code:
            queryset = queryset.filter(code=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if affiliation:
            queryset = queryset.filter(affiliation__icontains=affiliation)
        if specialization:
            queryset = queryset.filter(specialization=specialization)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new Expert with auto-generated code
        """
        try:
            with transaction.atomic():
                # Get the last running number for Expert (type 'E')
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='E').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1

                # Add code to request data
                request.data['code'] = f"E{nextNumber:04d}"

                # Create the expert
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)

                # If we got here, creation was successful - update running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='E',
                        year=current_year,
                        number=nextNumber
                    )

                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=201, headers=headers)
        except Exception as e:
            # Log the error and re-raise it
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating Expert: {str(e)}")
            raise

