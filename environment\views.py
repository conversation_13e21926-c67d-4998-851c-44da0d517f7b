from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Environment
from .serializers import EnvironmentSerializer
from rest_framework.response import Response
from django.db.models import Q
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time


@extend_schema(
    tags=["Environment"]
)
class EnvironmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Environment model providing CRUD operations.
    """
    queryset = Environment.objects.all()
    serializer_class = EnvironmentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']
    search_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        route__id = request.query_params.get('route__id')
        masProvince__id = request.query_params.get('masProvince__id')
        masDistrict__id = request.query_params.get('masDistrict__id')
        parameter = request.query_params.get('parameter')
        value = request.query_params.get('value')
        unit = request.query_params.get('unit')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        complianceStandard = request.query_params.get('complianceStandard')
        userId = request.query_params.get('userId')
        
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if parameter:
            queryset = queryset.filter(parameter__icontains=parameter)
        if value:
            queryset = queryset.filter(value=value)
        if unit:
            queryset = queryset.filter(unit__icontains=unit)
        if startDate:
            queryset = queryset.filter(measurementDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(measurementDate__lte=convert_str_to_date_max_time(endDate))
        if complianceStandard:
            queryset = queryset.filter(complianceStandard__icontains=complianceStandard)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
