from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from utils.pagination import CustomPagination
from .models import DownloadRequester, DownloadRequesterDetail, DownloadChartData
from .serializers import DownloadRequesterSerializer, DownloadRequesterDetailSerializer, DownloadChartDataSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from rest_framework import status
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from mas.models import Standard, MasUserType, MasPosition, MasUsagePurpose
from testings.models import Service, Testing
from research.models import Research
from course.models import Course
from manufacturer.models import Manufacturer
from components.models import Component
import logging
from datetime import datetime, timedelta
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time, convert_date_to_str_thai
from babel.dates import format_date
import io
import pandas as pd
import urllib.parse
from django.http import HttpResponse


@extend_schema(
    tags=["Download Requester"]
)
class DownloadRequesterViewSet(viewsets.ModelViewSet):
    queryset = DownloadRequester.objects.all()
    serializer_class = DownloadRequesterSerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = [
        'masUserType__id', 
        'departmentName', 
        'coordinatorName', 
        'masPosition__id', 
        'masUsagePurpose__id',
        ]
    menu_mapping = {
        'PartEquipment' : 'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง',
        'Standard' : 'ข้อมูลมาตรฐานระบบราง',
        'Service' : 'ข้อมูลศูนย์ทดสอบ',
        'Testing' : 'ข้อมูลการทดสอบ',
        'RailwayIndustrial' : 'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย',
        'Research' : 'ข้อมูลงานวิจัยและนวัตกรรม',
        'Course' : 'ข้อมูลหลักสูตร',
    }
    
    def list(self, request, *args, **kwargs):
        """
        List of download requester
        Parameters:
        - departmentName: str
        - coordinatorName: str
        - masUserTypeId: int
        - masPositionId: int
        - masUsagePurposeId: int
        - startDate: str (yyyy-mm-dd)
        - endDate: str (yyyy-mm-dd)
        - ordering: str
        """
        departmentName = request.query_params.get('departmentName')
        coordinatorName = request.query_params.get('coordinatorName')
        coordinatorEmail = request.query_params.get('coordinatorEmail')
        masUserTypeId = request.query_params.get('masUserType__id')
        masPositionId = request.query_params.get('masPosition__id')
        masUsagePurposeId = request.query_params.get('masUsagePurpose__id')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        ordering = request.query_params.get('ordering')
        if departmentName:
            self.queryset = self.queryset.filter(departmentName__icontains=departmentName)
        if coordinatorName:
            self.queryset = self.queryset.filter(coordinatorName__icontains=coordinatorName)
        if coordinatorEmail:
            self.queryset = self.queryset.filter(coordinatoremail__icontains=coordinatorEmail)
        if masUserTypeId:
            self.queryset = self.queryset.filter(masUserType__id=masUserTypeId)
        if masPositionId:
            self.queryset = self.queryset.filter(masPosition__id=masPositionId)
        if masUsagePurposeId:
            self.queryset = self.queryset.filter(masUsagePurpose__id=masUsagePurposeId)
        if startDate:
            self.queryset = self.queryset.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            self.queryset = self.queryset.filter(createDate__lte=convert_str_to_date_max_time(endDate))
        if ordering:
            self.queryset = self.queryset.order_by(ordering)
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            for item in serializer.data:
                downloadRequesterDetails = DownloadRequesterDetail.objects.filter(downloadRequester__id=item['id'])
                downloadRequesterDetailsSerializer = DownloadRequesterDetailSerializer(downloadRequesterDetails, many=True).data
                detail = []
                for detailItem in downloadRequesterDetailsSerializer:
                    detailText = ''
                    try:
                        match detailItem['type'].lower():
                            case 'standard':
                                try:
                                    standard = Standard.objects.get(id=detailItem['refid'])
                                    if standard:
                                        detailText = standard.name
                                except Standard.DoesNotExist:
                                    detailText = 'N/A'
                            case 'service':
                                try:
                                    service = Service.objects.get(id=detailItem['refid'])
                                    if service:
                                        detailText = service.detail
                                except Service.DoesNotExist:
                                    detailText = 'N/A'
                            case 'research':
                                try:
                                    research = Research.objects.get(id=detailItem['refid'])
                                    if research:
                                        detailText = research.projectName
                                except Research.DoesNotExist:
                                    detailText = 'N/A'
                            case 'course':
                                try:
                                    course = Course.objects.get(id=detailItem['refid'])
                                    if course:
                                        detailText = course.programTh
                                except Course.DoesNotExist:
                                    detailText = 'N/A'
                            case 'railwayindustrial':
                                try:
                                    manufacturer = Manufacturer.objects.get(id=detailItem['refid'])
                                    if manufacturer:
                                        detailText = manufacturer.name
                                except Manufacturer.DoesNotExist:
                                    detailText = 'N/A'
                            case 'partequipment':
                                try:
                                    component = Component.objects.get(id=detailItem['refid'])
                                    if component:
                                        detailText = component.name
                                except Component.DoesNotExist:
                                    detailText = 'N/A'
                    except Exception as e:
                        detailText = 'N/A'
                        logger = logging.getLogger(__name__)
                        logger.error(f"Error processing detail item: {str(e)}")
                    detail.append({
                        'type': detailItem['type'],
                        'refid': detailItem['refid'],
                        'detail': detailText,
                    })
                item['downloadRequesterDetails'] = detail
            return self.get_paginated_response(serializer.data)
        
        # If pagination is disabled
        serializer = self.get_serializer(queryset, many=True)
        for item in serializer.data:
            downloadRequesterDetails = DownloadRequesterDetail.objects.filter(downloadRequester__id=item['id'])
            item['downloadRequesterDetails'] = DownloadRequesterDetailSerializer(downloadRequesterDetails, many=True).data
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        # Get browser information from request headers
        user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
        # Truncate user_agent to 100 characters to prevent database truncation error
        user_agent = user_agent[:100] if user_agent else 'Unknown'

        # Get IP address from request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ipAddress = x_forwarded_for.split(',')[0].strip()
        else:
            ipAddress = request.META.get('REMOTE_ADDR', 'Unknown')
        request.data['browser'] = user_agent
        request.data['ipAddress'] = ipAddress
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['GET'], url_path='chart-data')
    def get_chart_data(self, request):
        """
        <h1>ข้อมูลกราฟ</h1>
        <p>Parameter</p>
        <ul>
            <li>type: (Required)</li>
            <ul>
                <li>week</li>
                <li>month</li>
                <li>year</li>
                <li>user-type</li>
                <li>position</li>
                <li>usage-purpose</li>
                <li>menu</li>
            </ul>
            <li>startDate: (Required)</li>
            <li>endDate: (Required)</li>
            <li>labelOrder: asc or desc (Optional)</li>
        </ul>
        """
        downloadChartData = DownloadChartData()
        labels = []
        datas = []
        type = request.query_params.get('type')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        labelOrder = request.query_params.get('labelOrder')
        backward = 7
        downloadRequesters = DownloadRequester.objects.all()
        if type:
            if type == 'stats':
                if startDate and endDate:
                    downloadRequesters = downloadRequesters.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_max_time(endDate))
                else:
                    downloadRequesters = downloadRequesters.all()
                labels = ['จำนวนการดาวน์โหลดทั้งหมด']
                datas = [0]
                for downloadRequester in downloadRequesters:
                    datas[0] += 1
                downloadChartData.label = labels
                downloadChartData.data = datas
            elif type == 'week':
                downloadRequesters = DownloadRequester.objects.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_min_time(endDate))
                downloadChartData = DownloadChartData()
                labels = []
                datas = []
                backward = (convert_str_to_date_min_time(endDate) - convert_str_to_date_min_time(startDate)).days + 1
                for i in range(backward):
                    date_obj = convert_str_to_date_min_time(startDate) + timedelta(days=i)
                    # Convert to Thai date format with Buddhist Era year (BE = CE + 543)
                    thai_month_day = format_date(date_obj, format='dd MMM', locale='th_TH')
                    buddhist_year = date_obj.year + 543
                    thai_date = f"{thai_month_day} {buddhist_year}"
                    labels.append(thai_date)
                    datas.append(0)
                    for downloadRequester in downloadRequesters:
                        if downloadRequester.createDate.date() == date_obj.date():
                            datas[i] += 1
                # Reverse the labels and data to show oldest to newest
                downloadChartData.label = list(reversed(labels))
                downloadChartData.data = list(reversed(datas))
            elif type == 'month':
                # Get data for the past 7 months
                startDate = convert_str_to_date_min_time(startDate)
                endDate = convert_str_to_date_min_time(endDate)
                downloadChartData = DownloadChartData()
                labels = []
                datas = []
                
                # Get the first day of the current month
                current_month_start = endDate.replace(day=1)
                backward = ((endDate.year - startDate.year) * 12) + (endDate.month - startDate.month) + 1
                
                for i in range(backward):
                    # Calculate the first day of each previous month
                    if i == 0:
                        month_start = current_month_start
                    else:
                        # Go to previous month
                        if month_start.month == 1:
                            month_start = month_start.replace(year=month_start.year-1, month=12, day=1)
                        else:
                            month_start = month_start.replace(month=month_start.month-1, day=1)
                    
                    # Calculate the last day of the month
                    if month_start.month == 12:
                        month_end = month_start.replace(year=month_start.year+1, month=1, day=1) - timedelta(days=1)
                    else:
                        month_end = month_start.replace(month=month_start.month+1, day=1) - timedelta(days=1)
                    
                    # Format the month name in Thai
                    thai_month = format_date(month_start, format='MMMM', locale='th_TH')
                    buddhist_year = month_start.year + 543
                    thai_date = f"{thai_month} {buddhist_year}"
                    
                    # Count DownloadRequester for this month
                    month_count = DownloadRequester.objects.filter(
                        createDate__date__gte=month_start.date(),
                        createDate__date__lte=month_end.date()
                    ).count()
                    
                    labels.append(thai_date)
                    datas.append(month_count)
                
                # Reverse to display oldest to newest
                downloadChartData.label = list(reversed(labels))
                downloadChartData.data = list(reversed(datas))
            elif type == 'year':
                startDate = convert_str_to_date_min_time(startDate)
                endDate = convert_str_to_date_min_time(endDate)
                downloadChartData = DownloadChartData()
                labels = []
                datas = []
                
                # Get the current year
                current_year = endDate.year
                backward = (endDate.year - startDate.year) + 1
                
                for i in range(backward):
                    # Calculate year
                    year = current_year - i
                    
                    # Start date is January 1st of the year
                    year_start = datetime(year, 1, 1).date()
                    
                    # End date is December 31st of the year
                    year_end = datetime(year, 12, 31).date()
                    
                    # Convert to Buddhist Era year (BE = CE + 543)
                    buddhist_year = year + 543
                    
                    # Count DownloadRequester for this year
                    year_count = DownloadRequester.objects.filter(
                        createDate__date__gte=year_start,
                        createDate__date__lte=year_end
                    ).count()
                    
                    labels.append(str(buddhist_year))
                    datas.append(year_count)
                
                # Reverse to display oldest to newest
                downloadChartData.label = list(reversed(labels))
                downloadChartData.data = list(reversed(datas))
            elif type == 'user-type':
                if startDate and endDate:
                    downloadRequesters = downloadRequesters.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_max_time(endDate))
                else:
                    downloadRequesters = downloadRequesters.all()
                masUserTypes = list(MasUserType.objects.all())
                masUserTypes.sort(key=lambda x: 999999 if x.id == 0 else x.id)  # Ensure id=0 is last
                labels = []
                for userType in masUserTypes:
                    labels.append(userType.name)
                datas = [0] * len(labels)
                for requester in downloadRequesters:
                    for i, userType in enumerate(masUserTypes):
                        if requester.masUserType.id == userType.id:
                            datas[i] += 1
                downloadChartData.label = labels
                downloadChartData.data = datas
            elif type == 'position':
                if startDate and endDate:
                    downloadRequesters = downloadRequesters.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_max_time(endDate))
                else:
                    downloadRequesters = downloadRequesters.all()
                masPositions = list(MasPosition.objects.all())
                masPositions.sort(key=lambda x: 999999 if x.id == 0 else x.id)  # Ensure id=0 is last
                labels = []
                for position in masPositions:
                    labels.append(position.name)
                datas = [0] * len(labels)
                for requester in downloadRequesters:
                    for i, position in enumerate(masPositions):
                        if requester.masPosition.id == position.id:
                            datas[i] += 1
                downloadChartData.label = labels
                downloadChartData.data = datas
            elif type == 'usage-purpose':
                if startDate and endDate:
                    downloadRequesters = downloadRequesters.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_max_time(endDate))
                else:
                    downloadRequesters = downloadRequesters.all()
                masUsagePurposes = list(MasUsagePurpose.objects.all())
                masUsagePurposes.sort(key=lambda x: 999999 if x.id == 0 else x.id)  # Ensure id=0 is last
                labels = []
                for purpose in masUsagePurposes:
                    labels.append(purpose.name)
                datas = [0] * len(labels)
                for requester in downloadRequesters:
                    for i, purpose in enumerate(masUsagePurposes):
                        if requester.masUsagePurpose.id == purpose.id:
                            datas[i] += 1
                downloadChartData.label = labels
                downloadChartData.data = datas
            elif type == 'menu':
                if startDate and endDate:
                    downloadRequesters = downloadRequesters.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_max_time(endDate))
                else:
                    downloadRequesters = downloadRequesters.all()
                labels = list(self.menu_mapping.values())
                menu_keys = list(self.menu_mapping.keys())
                datas = [0] * len(labels)
                for requester in downloadRequesters:
                    if hasattr(requester, 'menu') and requester.menu:
                        requester_menu = requester.menu.lower()
                        for i, key in enumerate(menu_keys):
                            if requester_menu == key.lower():
                                datas[i] += 1
                downloadChartData.label = labels
                downloadChartData.data = datas
            else:
                return Response({'error': 'type ' + type + ' is not supported'})
        else:
            return Response({'error': 'type is required'})
        return Response(DownloadChartDataSerializer(downloadChartData).data)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลผู้ดาวน์โหลด</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Download Requester IDs to include in the export
            "departmentName": "ชื่อหน่วยงาน"
            "coordinatorName": "ชื่อผู้ประสาน"
            "masUserType__id": 1
            "masPosition__id": 1
            "masUsagePurpose__id": 1
            "startDate": "2021-01-01"
            "endDate": "2021-01-01"
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        departmentName = request.query_params.get('departmentName')
        coordinatorName = request.query_params.get('coordinatorName')
        masUserTypeId = request.query_params.get('masUserType__id')
        masPositionId = request.query_params.get('masPosition__id')
        masUsagePurposeId = request.query_params.get('masUsagePurpose__id')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        queryset = self.get_queryset()
        if departmentName:
            queryset = queryset.filter(departmentName__icontains=departmentName)
        if coordinatorName:
            queryset = queryset.filter(coordinatorName__icontains=coordinatorName)
        if masUserTypeId:
            queryset = queryset.filter(masUserType__id=masUserTypeId)
        if masPositionId:
            queryset = queryset.filter(masPosition__id=masPositionId)
        if masUsagePurposeId:
            queryset = queryset.filter(masUsagePurpose__id=masUsagePurposeId)
        if startDate:
            queryset = queryset.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(createDate__lte=convert_str_to_date_max_time(endDate))
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
            
        for item in queryset:
            try:
                downloadRequesterDetails = DownloadRequesterDetail.objects.filter(downloadRequester=item)
                if downloadRequesterDetails:
                    item.menu = downloadRequesterDetails.first().type
                    item.refids = [downloadRequesterDetail.refid for downloadRequesterDetail in downloadRequesterDetails]
                else:
                    item.menu = None
                    item.refids = None
            except DownloadRequesterDetail.DoesNotExist:
                item.menu = None
                item.refids = None

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            item.refNames = []
            if item.menu:
                if item.menu == 'PartEquipment':
                    components = Component.objects.filter(id__in=item.refids)
                    item.refNames = [component.name for component in components]
                elif item.menu == 'Standard':
                    standards = Standard.objects.filter(id__in=item.refids)
                    item.refNames = [standard.name for standard in standards]
                elif item.menu == 'Service':
                    services = Service.objects.filter(id__in=item.refids)
                    item.refNames = [service.detail for service in services]
                elif item.menu == 'Testing':
                    tests = Testing.objects.filter(id__in=item.refids)
                    item.refNames = [test.name for test in tests]
                elif item.menu == 'RailwayIndustrial':
                    manufacturers = Manufacturer.objects.filter(id__in=item.refids)
                    item.refNames = [manufacturer.name for manufacturer in manufacturers]
                elif item.menu == 'Research':
                    researches = Research.objects.filter(id__in=item.refids)
                    item.refNames = [research.projectName for research in researches]
                elif item.menu == 'Course':
                    courses = Course.objects.filter(id__in=item.refids)
                    item.refNames = [course.programTh for course in courses]
                    
            excel_data.append({
                'ลำดับ': count,
                'วันที่ดาวน์โหลด': convert_date_to_str_thai(item.createDate,with_time=True),
                'ประเภท': item.masUserType.name,
                'ชื่อหน่วยงาน': item.departmentName,
                'ชื่อผู้ประสาน': item.coordinatorName,
                'ตำแหน่ง': item.masPosition.name if item.masPosition else None,
                'วัตถุประสงค์ในการนำไปใช้': item.masUsagePurpose.name if not (item.masUsagePurpose.id == 0 or item.masUsagePurpose.name == 'อื่นๆ') else (item.masUsagePurpose.name + ' ' + item.usagePurposeOther),
                'ชื่อเมนู': self.menu_mapping[item.menu] if item.menu else None,
                'ชื่อข้อมูล': chr(10).join(item.refNames) if item.refNames else None,
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'รายการบันทึกการใช้งานดาวน์โหลด'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:I1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:I2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Download Requester"]
)
class DownloadRequesterDetailViewSet(viewsets.ModelViewSet):
    queryset = DownloadRequesterDetail.objects.all()
    serializer_class = DownloadRequesterDetailSerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]
    
    def create(self, request, *args, **kwargs):
        downloadRequesterId = request.data.get('downloadRequesterId')
        type = request.data.get('type')
        refids = request.data.get('refids')
        if refids:
            for id in refids:
                try:
                    DownloadRequesterDetail.objects.create(
                        downloadRequester=DownloadRequester.objects.get(id=downloadRequesterId),
                        type=type,
                        refid=id,
                    )
                except Exception as e:
                    return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR, data={"message": str(e)})
        return Response(status=status.HTTP_201_CREATED)
