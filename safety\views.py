from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time
from django.db.models import Q

from .models import Safety
from .serializers import SafetySerializer


class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


@extend_schema(
    tags=["Safety"]
)
class SafetyViewSet(viewsets.ModelViewSet):
    queryset = Safety.objects.all()
    serializer_class = SafetySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']
    search_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']

    def list(self, request, *args, **kwargs):
        systemName = request.query_params.get('systemName')
        safetyFeature = request.query_params.get('safetyFeature')
        safetyStandard = request.query_params.get('safetyStandard')
        riskLevel = request.query_params.get('riskLevel')
        complianceStatus = request.query_params.get('complianceStatus')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        userId = request.query_params.get('userId')

        if systemName:
            self.queryset = self.queryset.filter(systemName__icontains=systemName)
        if safetyFeature:
            self.queryset = self.queryset.filter(safetyFeature__icontains=safetyFeature)
        if safetyStandard:
            self.queryset = self.queryset.filter(safetyStandard__icontains=safetyStandard)
        if riskLevel:
            self.queryset = self.queryset.filter(riskLevel=riskLevel)
        if complianceStatus:
            self.queryset = self.queryset.filter(complianceStatus__icontains=complianceStatus)
        if startDate:
            self.queryset = self.queryset.filter(lastAuditDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            self.queryset = self.queryset.filter(lastAuditDate__lte=convert_str_to_date_max_time(endDate))
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

